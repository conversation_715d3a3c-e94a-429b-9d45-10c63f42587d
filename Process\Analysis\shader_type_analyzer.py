#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于语法树的精确类型分析器
"""

from typing import Dict, List, Any

from .syntax_tree_builder import SyntaxTreeAnalyzer, LineAnalysisResult

class ShaderTypeAnalyzer:
    """基于语法树的类型分析器"""

    def __init__(self):
        self.syntax_analyzer = SyntaxTreeAnalyzer()
    


    def analyze_shader_with_precise_types(self, shader_content: str) -> Dict:
        """分析着色器代码并进行精确类型分析"""
        # 使用语法树分析器进行完整分析
        syntax_result = self.syntax_analyzer.analyze_shader_with_syntax_trees(shader_content)



        # 整合结果
        result = {
            'code_lines': syntax_result['code_lines'],
            'line_analyses': syntax_result['line_analyses'],
            'global_type_table': syntax_result['global_type_table'],
            'overall_statistics': self._generate_overall_statistics(syntax_result['line_analyses'])
        }

        return result

    def _generate_overall_statistics(self, line_analyses: List[LineAnalysisResult]) -> Dict:
        """生成整体统计信息"""
        overall_stats = {
            'total_lines': len(line_analyses),
            'total_nodes': 0,
            'total_variables': 0,
            'total_intermediate_results': 0,
            'total_type_conversions': 0,
            'total_precision_issues': 0,
            'type_distribution': {},
            'precision_accuracy_score': 0.0
        }

        total_confidence_sum = 0
        total_confidence_count = 0

        for analysis in line_analyses:
            if analysis.has_error:
                continue

            overall_stats['total_nodes'] += len(analysis.typed_nodes)
            overall_stats['total_variables'] += len(analysis.variable_types)
            overall_stats['total_intermediate_results'] += len(analysis.intermediate_results)
            overall_stats['total_type_conversions'] += len(analysis.type_conversions)
            overall_stats['total_precision_issues'] += len(analysis.precision_issues)

            # 统计类型分布
            for typed_node in analysis.typed_nodes:
                type_name = typed_node.inferred_type.value
                overall_stats['type_distribution'][type_name] = \
                    overall_stats['type_distribution'].get(type_name, 0) + 1

            # 累计置信度
            for typed_node in analysis.typed_nodes:
                total_confidence_sum += typed_node.type_confidence
                total_confidence_count += 1

        # 计算精度准确性评分
        if total_confidence_count > 0:
            overall_stats['precision_accuracy_score'] = \
                (total_confidence_sum / total_confidence_count) * 100

        return overall_stats

