#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法树构建器 - 构建着色器代码的语法树结构并进行类型分析
"""

import re
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
from .code_line_analyzer import CodeLineAnalyzer

class NodeType(Enum):
    """节点类型枚举"""
    VARIABLE = "variable"           # 变量
    LITERAL = "literal"            # 字面量
    OPERATOR = "operator"          # 运算符
    FUNCTION = "function"          # 函数调用
    ASSIGNMENT = "assignment"      # 赋值
    DECLARATION = "declaration"    # 声明
    MEMBER_ACCESS = "member_access" # 成员访问
    TYPE_CAST = "type_cast"        # 类型转换

class DataType(Enum):
    """数据类型枚举"""
    FLOAT = "float"
    HALF = "half"
    INT = "int"
    UINT = "uint"
    BOOL = "bool"
    FLOAT2 = "float2"
    FLOAT3 = "float3"
    FLOAT4 = "float4"
    HALF2 = "half2"
    HALF3 = "half3"
    HALF4 = "half4"
    FLOAT3X3 = "float3x3"
    FLOAT4X4 = "float4x4"
    UNKNOWN = "unknown"

@dataclass
class ASTNode:
    """抽象语法树节点"""
    node_type: NodeType
    value: str                   # 节点值（变量名、运算符、函数名等）
    data_type: DataType          # 数据类型
    children: List['ASTNode']    # 子节点
    line_number: int             # 源代码行号
    position: int = 0            # 在行中的位置
    
    def add_child(self, child: 'ASTNode'):
        """添加子节点"""
        self.children.append(child)
    
    def __str__(self):
        return f"{self.node_type.value}({self.value}:{self.data_type.value})"

@dataclass
class TypedNode:
    """带类型信息的节点"""
    node: ASTNode
    inferred_type: DataType
    is_intermediate: bool = False  # 是否是中间结果
    type_confidence: float = 1.0   # 类型推断置信度

class LineAnalysisResult:
    """单行代码分析结果"""

    def __init__(self, line_number: int, line_content: str):
        self.line_number = line_number
        self.line_content = line_content
        self.syntax_tree: Optional[ASTNode] = None
        self.typed_nodes: List[TypedNode] = []
        self.variable_types: Dict[str, DataType] = {}
        self.intermediate_results: List[TypedNode] = []
        self.type_conversions: List[Dict] = []
        self.precision_issues: List[Dict] = []
        self.simulation_data: Dict[str, Any] = {}
        self.has_error: bool = False
        self.error_message: str = ""

class TypeTable:
    """全局类型表"""
    
    def __init__(self):
        self.variables: Dict[str, DataType] = {}
        self.functions: Dict[str, Dict] = {}
        self._init_builtin_functions()
    
    def _init_builtin_functions(self):
        """初始化内置函数"""
        self.functions.update({
            'normalize': {'return_type': DataType.UNKNOWN, 'params': ['vector']},
            'dot': {'return_type': DataType.FLOAT, 'params': ['vector', 'vector']},
            'cross': {'return_type': DataType.UNKNOWN, 'params': ['vector', 'vector']},
            'max': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any']},
            'min': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any']},
            'clamp': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any', 'any']},
            'mix': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any', 'float']},
            'sqrt': {'return_type': DataType.UNKNOWN, 'params': ['any']},
            'pow': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any']},
            'sin': {'return_type': DataType.UNKNOWN, 'params': ['any']},
            'cos': {'return_type': DataType.UNKNOWN, 'params': ['any']},
            'sample': {'return_type': DataType.HALF4, 'params': ['texture', 'sampler', 'coords']},
        })
    
    def declare_variable(self, name: str, data_type: DataType):
        """声明变量"""
        self.variables[name] = data_type
    
    def get_variable_type(self, name: str) -> DataType:
        """获取变量类型"""
        return self.variables.get(name, DataType.UNKNOWN)
    
    def get_function_info(self, name: str) -> Dict:
        """获取函数信息"""
        return self.functions.get(name, {'return_type': DataType.UNKNOWN, 'params': []})

class SyntaxTreeBuilder:
    """语法树构建器"""
    
    def __init__(self):
        self.type_table = TypeTable()
        self.operators = {
            '+': 'add', '-': 'sub', '*': 'mul', '/': 'div',
            '=': 'assign', '==': 'eq', '!=': 'ne',
            '<': 'lt', '>': 'gt', '<=': 'le', '>=': 'ge',
            '&&': 'and', '||': 'or', '!': 'not'
        }


        # 运算符类型推断规则
        self.operator_type_rules = {
            '+': self._infer_arithmetic_type,
            '-': self._infer_arithmetic_type,
            '*': self._infer_arithmetic_type,
            '/': self._infer_arithmetic_type,
            '==': lambda l, r: DataType.BOOL,
            '!=': lambda l, r: DataType.BOOL,
            '<': lambda l, r: DataType.BOOL,
            '>': lambda l, r: DataType.BOOL,
            '<=': lambda l, r: DataType.BOOL,
            '>=': lambda l, r: DataType.BOOL,
            '&&': lambda l, r: DataType.BOOL,
            '||': lambda l, r: DataType.BOOL,
        }

        # 函数返回类型推断规则
        self.function_return_rules = {
            'normalize': self._infer_normalize_return,
            'dot': lambda args: DataType.FLOAT,
            'cross': self._infer_cross_return,
            'max': self._infer_max_min_return,
            'min': self._infer_max_min_return,
            'clamp': self._infer_clamp_return,
            'mix': self._infer_mix_return,
            'sqrt': lambda args: args[0] if args else DataType.floa,
            'pow': lambda args: args[0] if args else DataType.UNKNOWN,
            'sin': lambda args: args[0] if args else DataType.UNKNOWN,
            'cos': lambda args: args[0] if args else DataType.UNKNOWN,
        }
    
    def parse_type(self, type_str: str) -> DataType:
        """解析类型字符串"""
        type_map = {
            'float': DataType.FLOAT, 'half': DataType.HALF,
            'int': DataType.INT, 'uint': DataType.UINT, 'bool': DataType.BOOL,
            'float2': DataType.FLOAT2, 'float3': DataType.FLOAT3, 'float4': DataType.FLOAT4,
            'half2': DataType.HALF2, 'half3': DataType.HALF3, 'half4': DataType.HALF4,
            'float3x3': DataType.FLOAT3X3, 'float4x4': DataType.FLOAT4X4
        }
        return type_map.get(type_str.lower(), DataType.UNKNOWN)
    
    def infer_literal_type(self, value: str) -> DataType:
        """推断字面量类型"""
        if re.match(r'^\d+\.\d*f?$', value):
            return DataType.FLOAT
        elif re.match(r'^\d+$', value):
            return DataType.INT
        elif value.lower() in ['true', 'false']:
            return DataType.BOOL
        return DataType.UNKNOWN
    
    def parse_expression(self, expr: str, line_number: int) -> ASTNode:
        """解析表达式"""
        expr = expr.strip()

        # 移除外层括号
        expr = self._remove_outer_parentheses(expr)

        # 跳过空初始化表达式
        if expr in ['{}', '{ }'] or re.match(r'^\{\s*\}$', expr):
            return ASTNode(
                node_type=NodeType.LITERAL,
                value="{}",
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=line_number
            )

        # 处理类型转换 float(x), half(x)
        type_cast_match = re.match(r'^(float|half|float2|float3|float4|half2|half3|half4)\s*\((.+)\)$', expr)
        if type_cast_match:
            target_type_str, inner_expr = type_cast_match.groups()
            target_type = self.parse_type(target_type_str)

            cast_node = ASTNode(
                node_type=NodeType.TYPE_CAST,
                value=f"{target_type_str}()",
                data_type=target_type,
                children=[],
                line_number=line_number
            )

            inner_node = self.parse_expression(inner_expr, line_number)
            cast_node.add_child(inner_node)
            return cast_node
        
        # 处理函数调用 func(a, b, c)
        func_match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*\((.+)\)$', expr)
        if func_match:
            func_name, params_str = func_match.groups()
            func_info = self.type_table.get_function_info(func_name)
            
            func_node = ASTNode(
                node_type=NodeType.FUNCTION,
                value=func_name,
                data_type=func_info['return_type'],
                children=[],
                line_number=line_number
            )
            
            # 解析参数
            if params_str.strip():
                params = self._split_parameters(params_str)
                for param in params:
                    param_node = self.parse_expression(param.strip(), line_number)
                    func_node.add_child(param_node)
            
            return func_node
        
        # 处理成员访问 obj.member
        member_match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*\.\s*([a-zA-Z_][a-zA-Z0-9_]*)$', expr)
        if member_match:
            obj_name, member_name = member_match.groups()
            obj_type = self.type_table.get_variable_type(obj_name)
            
            member_node = ASTNode(
                node_type=NodeType.MEMBER_ACCESS,
                value=f"{obj_name}.{member_name}",
                data_type=self._infer_member_type(obj_type, member_name),
                children=[],
                line_number=line_number
            )
            
            return member_node
        
        # 处理二元运算符（按优先级顺序）
        # 优先级从低到高：逻辑运算 -> 比较运算 -> 加减 -> 乘除
        operator_groups = [
            ['||'],                    # 逻辑或
            ['&&'],                    # 逻辑与
            ['==', '!='],              # 相等比较
            ['<', '>', '<=', '>='],    # 大小比较
            ['+', '-'],                # 加减
            ['*', '/']                 # 乘除
        ]

        # 从低优先级到高优先级查找运算符
        for operators in operator_groups:
            for op in operators:
                # 查找最右边的运算符（左结合）
                op_pos = expr.rfind(op)
                if op_pos > 0:  # 确保不是开头的负号
                    left_expr = expr[:op_pos].strip()
                    right_expr = expr[op_pos + len(op):].strip()

                    if left_expr and right_expr:
                        # 检查是否在括号内
                        if self._is_operator_in_parentheses(expr, op_pos):
                            continue

                        op_node = ASTNode(
                            node_type=NodeType.OPERATOR,
                            value=op,
                            data_type=DataType.UNKNOWN,  # 稍后推断
                            children=[],
                            line_number=line_number
                        )

                        left_node = self.parse_expression(left_expr, line_number)
                        right_node = self.parse_expression(right_expr, line_number)

                        op_node.add_child(left_node)
                        op_node.add_child(right_node)

                        return op_node
        
        # 处理变量或字面量
        if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', expr):
            # 变量
            var_type = self.type_table.get_variable_type(expr)
            return ASTNode(
                node_type=NodeType.VARIABLE,
                value=expr,
                data_type=var_type,
                children=[],
                line_number=line_number
            )
        else:
            # 字面量
            literal_type = self.infer_literal_type(expr)
            return ASTNode(
                node_type=NodeType.LITERAL,
                value=expr,
                data_type=literal_type,
                children=[],
                line_number=line_number
            )
    
    def _split_parameters(self, params_str: str) -> List[str]:
        """分割函数参数（考虑嵌套括号）"""
        params = []
        current_param = ""
        paren_count = 0
        
        for char in params_str:
            if char == ',' and paren_count == 0:
                params.append(current_param.strip())
                current_param = ""
            else:
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                current_param += char
        
        if current_param.strip():
            params.append(current_param.strip())
        
        return params

    def _is_operator_in_parentheses(self, expr: str, op_pos: int) -> bool:
        """检查运算符是否在括号内"""
        paren_count = 0
        for i, char in enumerate(expr):
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif i == op_pos and paren_count > 0:
                return True
        return False

    def _remove_outer_parentheses(self, expr: str) -> str:
        """移除外层括号"""
        expr = expr.strip()
        if expr.startswith('(') and expr.endswith(')'):
            # 检查是否是完整的外层括号
            paren_count = 0
            for i, char in enumerate(expr):
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                    if paren_count == 0 and i < len(expr) - 1:
                        return expr  # 不是外层括号
            return expr[1:-1].strip()
        return expr

    def _infer_member_type(self, obj_type: DataType, member: str) -> DataType:
        """推断成员访问的类型"""
        if member in ['x', 'y', 'z', 'w', 'r', 'g', 'b', 'a']:
            return DataType.FLOAT if obj_type.value.startswith('float') else DataType.HALF
        elif member in ['xy', 'xyz', 'rgb', 'rg']:
            if member == 'xy' or member == 'rg':
                return DataType.FLOAT2 if obj_type.value.startswith('float') else DataType.HALF2
            elif member == 'xyz' or member == 'rgb':
                return DataType.FLOAT3 if obj_type.value.startswith('float') else DataType.HALF3
        return DataType.UNKNOWN
    
    def _infer_operator_result_type(self, op: str, left_expr: str, right_expr: str) -> DataType:
        """推断运算符结果类型"""
        if op in ['==', '!=', '<', '>', '<=', '>=', '&&', '||']:
            return DataType.BOOL
        # 对于算术运算符，需要更复杂的类型推断
        return DataType.UNKNOWN
    
    def parse_statement(self, statement: str, line_number: int) -> ASTNode:
        """解析语句"""
        statement = statement.strip().rstrip(';')

        # 处理变量声明和赋值 type var = expr
        decl_match = re.match(r'^(float|half|float2|float3|float4|half2|half3|half4|float3x3|float4x4|int|uint|bool)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(.+)$', statement)
        if decl_match:
            type_str, var_name, expr = decl_match.groups()
            var_type = self.parse_type(type_str)

            # 在类型表中声明变量
            self.type_table.declare_variable(var_name, var_type)

            # 创建赋值节点作为根节点（=运算符）
            assign_node = ASTNode(
                node_type=NodeType.ASSIGNMENT,
                value="=",
                data_type=var_type,
                children=[],
                line_number=line_number
            )

            # 创建变量声明节点作为左子节点
            var_node = ASTNode(
                node_type=NodeType.DECLARATION,
                value=f"{type_str} {var_name}",
                data_type=var_type,
                children=[],
                line_number=line_number
            )

            # 解析右侧表达式作为右子节点
            expr_node = self.parse_expression(expr, line_number)

            # 构建树结构：= 为根，变量声明为左子节点，表达式为右子节点
            assign_node.add_child(var_node)
            assign_node.add_child(expr_node)

            return assign_node

        # 处理赋值 var = expr
        assign_match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(.+)$', statement)
        if assign_match:
            var_name, expr = assign_match.groups()
            var_type = self.type_table.get_variable_type(var_name)

            # 创建赋值节点作为根节点（=运算符）
            assign_node = ASTNode(
                node_type=NodeType.ASSIGNMENT,
                value="=",
                data_type=var_type,
                children=[],
                line_number=line_number
            )

            # 创建变量节点作为左子节点
            var_node = ASTNode(
                node_type=NodeType.VARIABLE,
                value=var_name,
                data_type=var_type,
                children=[],
                line_number=line_number
            )

            # 解析右侧表达式作为右子节点
            expr_node = self.parse_expression(expr, line_number)

            # 构建树结构：= 为根，变量为左子节点，表达式为右子节点
            assign_node.add_child(var_node)
            assign_node.add_child(expr_node)

            return assign_node

        # 处理其他表达式
        return self.parse_expression(statement, line_number)
    
    def build_syntax_trees(self, code_lines: List[str]) -> List[ASTNode]:
        """为代码行列表构建语法树"""
        trees = []
        in_block_comment = False  # 跟踪是否在块注释中

        for i, line in enumerate(code_lines, 1):
            line = line.strip()

            # 处理块注释
            line, in_block_comment = self._process_block_comments(line, in_block_comment)

            # 跳过空行、单行注释和块注释内容
            if (line and
                not line.startswith('//') and
                not line.startswith('#') and
                not in_block_comment and
                line != ''):
                try:
                    tree = self.parse_statement(line, i)
                    trees.append(tree)
                except Exception as e:
                    # 创建错误节点
                    error_node = ASTNode(
                        node_type=NodeType.VARIABLE,
                        value=f"ERROR: {str(e)}",
                        data_type=DataType.UNKNOWN,
                        children=[],
                        line_number=i
                    )
                    trees.append(error_node)
        return trees

    def _process_block_comments(self, line: str, in_block_comment: bool) -> tuple[str, bool]:
        """
        处理块注释 /* */
        返回: (处理后的行内容, 是否仍在块注释中)
        """
        result_line = line
        current_in_comment = in_block_comment
        # 如果已经在块注释中，查找结束标记
        if current_in_comment:
            end_pos = result_line.find('*/')
            if end_pos != -1:
                # 找到块注释结束，保留结束标记之后的内容
                result_line = result_line[end_pos + 2:]
                current_in_comment = False
                # 递归处理剩余内容，可能还有其他注释
                result_line, current_in_comment = self._process_block_comments(result_line, current_in_comment)
            else:
                # 整行都在块注释中
                result_line = ''
        else:
            # 不在块注释中，查找开始标记
            start_pos = result_line.find('/*')
            if start_pos != -1:
                # 找到块注释开始
                before_comment = result_line[:start_pos]
                after_start = result_line[start_pos + 2:]

                # 查找同一行中的结束标记
                end_pos = after_start.find('*/')
                if end_pos != -1:
                    # 同一行中有完整的块注释
                    after_comment = after_start[end_pos + 2:]
                    result_line = before_comment + after_comment
                    # 递归处理剩余内容
                    result_line, current_in_comment = self._process_block_comments(result_line, current_in_comment)
                else:
                    result_line = before_comment
                    current_in_comment = True

        return result_line.strip(), current_in_comment

    def print_tree(self, node: ASTNode, indent: int = 0) -> str:
        """打印语法树结构"""
        result = "  " * indent + f"{node}\n"
        for child in node.children:
            result += self.print_tree(child, indent + 1)
        return result

    def analyze_tree_types(self, node: ASTNode) -> Dict[str, Any]:
        """分析语法树中的类型信息"""
        analysis = {
            'node_info': {
                'type': node.node_type.value,
                'value': node.value,
                'data_type': node.data_type.value,
                'line': node.line_number
            },
            'type_conversions': [],
            'precision_issues': [],
            'children_analysis': []
        }

        # 检查类型转换
        if node.node_type == NodeType.TYPE_CAST:
            if node.children:
                source_type = node.children[0].data_type
                target_type = node.data_type
                analysis['type_conversions'].append({
                    'from': source_type.value,
                    'to': target_type.value,
                    'line': node.line_number
                })

        # 检查精度问题
        if node.node_type == NodeType.OPERATOR and node.value in ['+', '-', '*', '/']:
            if len(node.children) >= 2:
                left_type = node.children[0].data_type
                right_type = node.children[1].data_type

                if self._is_mixed_precision(left_type, right_type):
                    analysis['precision_issues'].append({
                        'operation': node.value,
                        'left_type': left_type.value,
                        'right_type': right_type.value,
                        'line': node.line_number
                    })

        # 递归分析子节点
        for child in node.children:
            child_analysis = self.analyze_tree_types(child)
            analysis['children_analysis'].append(child_analysis)

            # 合并子节点的类型转换和精度问题
            analysis['type_conversions'].extend(child_analysis['type_conversions'])
            analysis['precision_issues'].extend(child_analysis['precision_issues'])

        return analysis

    def _is_mixed_precision(self, type1: DataType, type2: DataType) -> bool:
        """检查是否是混合精度运算"""
        float_types = {DataType.FLOAT, DataType.FLOAT2, DataType.FLOAT3, DataType.FLOAT4}
        half_types = {DataType.HALF, DataType.HALF2, DataType.HALF3, DataType.HALF4}

        return (type1 in float_types and type2 in half_types) or \
               (type1 in half_types and type2 in float_types)

    def get_type_summary(self, trees: List[ASTNode]) -> Dict[str, Any]:
        """获取所有语法树的类型摘要"""
        summary = {
            'total_trees': len(trees),
            'variable_declarations': 0,
            'assignments': 0,
            'function_calls': 0,
            'type_conversions': 0,
            'precision_issues': 0,
            'declared_variables': dict(self.type_table.variables),
            'type_conversion_details': [],
            'precision_issue_details': []
        }

        for tree in trees:
            analysis = self.analyze_tree_types(tree)

            # 统计节点类型
            if tree.node_type == NodeType.DECLARATION:
                summary['variable_declarations'] += 1
            elif tree.node_type == NodeType.ASSIGNMENT:
                summary['assignments'] += 1
            elif tree.node_type == NodeType.FUNCTION:
                summary['function_calls'] += 1

            # 统计类型转换和精度问题
            summary['type_conversions'] += len(analysis['type_conversions'])
            summary['precision_issues'] += len(analysis['precision_issues'])
            summary['type_conversion_details'].extend(analysis['type_conversions'])
            summary['precision_issue_details'].extend(analysis['precision_issues'])

        return summary




    def analyze_line_with_types(self, line_content: str, line_number: int, global_type_table: Dict[str, DataType] = None) -> LineAnalysisResult:
        """分析单行代码，包括语法树构建、类型推断和模拟"""
        if global_type_table is None:
            global_type_table = {}

        result = LineAnalysisResult(line_number, line_content)

        try:
            # 构建语法树
            result.syntax_tree = self.parse_statement(line_content, line_number)

            # 进行类型分析
            self._analyze_node_types_recursive(result.syntax_tree, global_type_table, result)

            # 生成模拟数据
            result.simulation_data = {
                'line_number': line_number,
                'original_code': line_content,
                'simulation_steps': [],
                'temp_variables_used': [],
                'has_simulation': False
            }

        except Exception as e:
            result.has_error = True
            result.error_message = str(e)
            # 创建错误节点
            result.syntax_tree = ASTNode(
                node_type=NodeType.VARIABLE,
                value=f"ERROR: {str(e)}",
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=line_number
            )

        return result

    def _analyze_node_types_recursive(self, node: ASTNode, type_table: Dict[str, DataType], result: LineAnalysisResult):
        """递归分析节点类型"""
        # 先分析子节点
        child_types = []
        for child in node.children:
            self._analyze_node_types_recursive(child, type_table, result)
            child_types.append(self._get_node_inferred_type(child, type_table, result))

        # 推断当前节点类型
        inferred_type = self._infer_node_type(node, child_types, type_table)

        # 创建类型化节点
        typed_node = TypedNode(
            node=node,
            inferred_type=inferred_type,
            is_intermediate=self._is_intermediate_result(node),
            type_confidence=self._calculate_confidence(node, inferred_type)
        )

        result.typed_nodes.append(typed_node)

        # 如果是中间结果，添加到中间结果列表
        if typed_node.is_intermediate:
            result.intermediate_results.append(typed_node)

        # 如果是变量声明，更新类型表
        if node.node_type == NodeType.DECLARATION:
            var_name = self._extract_variable_name(node.value)
            if var_name:
                type_table[var_name] = inferred_type
                result.variable_types[var_name] = inferred_type

        # 如果是赋值运算，更新左侧变量的类型
        elif node.node_type == NodeType.ASSIGNMENT and len(node.children) >= 2:
            left_child = node.children[0]
            if left_child.node_type == NodeType.VARIABLE:
                var_name = left_child.value
                type_table[var_name] = inferred_type
                result.variable_types[var_name] = inferred_type

        # 检查类型转换
        if node.node_type == NodeType.TYPE_CAST and child_types:
            source_type = child_types[0]
            target_type = inferred_type
            if source_type != target_type and source_type != DataType.UNKNOWN:
                result.type_conversions.append({
                    'node': node,
                    'from_type': source_type,
                    'to_type': target_type,
                    'line': node.line_number
                })

        # 检查赋值运算的隐式类型转换
        if node.node_type == NodeType.ASSIGNMENT and len(child_types) >= 2:
            left_type = child_types[0]   # 左侧变量类型
            right_type = child_types[1]  # 右侧表达式类型

            # 如果左右类型不同，说明发生了隐式类型转换
            if (left_type != right_type and
                left_type != DataType.UNKNOWN and
                right_type != DataType.UNKNOWN):
                result.type_conversions.append({
                    'node': node,
                    'from_type': right_type,
                    'to_type': left_type,
                    'line': node.line_number,
                    'conversion_type': 'implicit_assignment'  # 标记为隐式赋值转换
                })

        # 检查混合精度问题
        if node.node_type == NodeType.OPERATOR and len(child_types) >= 2:
            if self._is_mixed_precision_operation(child_types[0], child_types[1]):
                result.precision_issues.append({
                    'node': node,
                    'left_type': child_types[0],
                    'right_type': child_types[1],
                    'operation': node.value,
                    'line': node.line_number
                })

    def _infer_node_type(self, node: ASTNode, child_types: List[DataType], type_table: Dict[str, DataType]) -> DataType:
        """推断节点类型"""
        if node.node_type == NodeType.VARIABLE:
            return type_table.get(node.value, DataType.UNKNOWN)

        elif node.node_type == NodeType.LITERAL:
            return self._infer_literal_type(node.value)

        elif node.node_type == NodeType.OPERATOR:
            if node.value in self.operator_type_rules and len(child_types) >= 2:
                result_type = self.operator_type_rules[node.value](child_types[0], child_types[1])
                return result_type
            return DataType.UNKNOWN

        elif node.node_type == NodeType.FUNCTION:
            if node.value in self.function_return_rules:
                return self.function_return_rules[node.value](child_types)
            return DataType.UNKNOWN

        elif node.node_type == NodeType.TYPE_CAST:
            return node.data_type

        elif node.node_type == NodeType.DECLARATION:
            return node.data_type

        elif node.node_type == NodeType.ASSIGNMENT:
            var_name = self._extract_variable_name(node.value)
            return type_table.get(var_name, DataType.UNKNOWN)

        elif node.node_type == NodeType.MEMBER_ACCESS:
            return self._infer_member_access_type(node.value, type_table)

        return DataType.UNKNOWN

    def _infer_arithmetic_type(self, left_type: DataType, right_type: DataType) -> DataType:
        """推断算术运算结果类型"""
        # 如果有未知类型，返回另一个类型
        if left_type == DataType.UNKNOWN:
            return right_type
        if right_type == DataType.UNKNOWN:
            return left_type

        # 类型提升规则：float > half > int
        type_priority = {
            DataType.FLOAT: 3, DataType.FLOAT2: 3, DataType.FLOAT3: 3, DataType.FLOAT4: 3,
            DataType.HALF: 2, DataType.HALF2: 2, DataType.HALF3: 2, DataType.HALF4: 2,
            DataType.INT: 1, DataType.UINT: 1,
            DataType.BOOL: 0
        }

        left_priority = type_priority.get(left_type, 0)
        right_priority = type_priority.get(right_type, 0)

        # 返回优先级更高的类型
        if left_priority >= right_priority:
            return left_type
        else:
            return right_type

    def _infer_normalize_return(self, arg_types: List[DataType]) -> DataType:
        """推断normalize函数返回类型"""
        if not arg_types:
            return DataType.UNKNOWN
        return arg_types[0]  # normalize返回与输入相同的向量类型

    def _infer_cross_return(self, arg_types: List[DataType]) -> DataType:
        """推断cross函数返回类型"""
        if len(arg_types) >= 2:
            # cross总是返回3D向量
            if arg_types[0].value.startswith('float'):
                return DataType.FLOAT3
            elif arg_types[0].value.startswith('half'):
                return DataType.HALF3
        return DataType.FLOAT3

    def _infer_max_min_return(self, arg_types: List[DataType]) -> DataType:
        """推断max/min函数返回类型"""
        if len(arg_types) >= 2:
            return self._infer_arithmetic_type(arg_types[0], arg_types[1])
        return DataType.UNKNOWN

    def _infer_clamp_return(self, arg_types: List[DataType]) -> DataType:
        """推断clamp函数返回类型"""
        if arg_types:
            return arg_types[0]  # clamp返回第一个参数的类型
        return DataType.UNKNOWN

    def _infer_mix_return(self, arg_types: List[DataType]) -> DataType:
        """推断mix函数返回类型"""
        if len(arg_types) >= 2:
            return self._infer_arithmetic_type(arg_types[0], arg_types[1])
        return DataType.UNKNOWN

    def _infer_literal_type(self, value: str) -> DataType:
        """推断字面量类型"""
        import re
        if re.match(r'^\d+\.\d*f?$', value):
            return DataType.FLOAT
        elif re.match(r'^\d+$', value):
            return DataType.INT
        elif value.lower() in ['true', 'false']:
            return DataType.BOOL
        return DataType.UNKNOWN

    def _infer_member_access_type(self, member_expr: str, type_table: Dict[str, DataType]) -> DataType:
        """推断成员访问类型"""
        parts = member_expr.split('.')
        if len(parts) != 2:
            return DataType.UNKNOWN

        obj_name, member = parts
        obj_type = type_table.get(obj_name, DataType.UNKNOWN)

        # 根据成员名推断类型
        if member in ['x', 'y', 'z', 'w', 'r', 'g', 'b', 'a']:
            if obj_type.value.startswith('float'):
                return DataType.FLOAT
            elif obj_type.value.startswith('half'):
                return DataType.HALF
        elif member in ['xy', 'rg']:
            if obj_type.value.startswith('float'):
                return DataType.FLOAT2
            elif obj_type.value.startswith('half'):
                return DataType.HALF2
        elif member in ['xyz', 'rgb']:
            if obj_type.value.startswith('float'):
                return DataType.FLOAT3
            elif obj_type.value.startswith('half'):
                return DataType.HALF3

        return DataType.UNKNOWN

    def _is_intermediate_result(self, node: ASTNode) -> bool:
        """判断是否是中间结果"""
        return node.node_type in [NodeType.OPERATOR, NodeType.FUNCTION, NodeType.TYPE_CAST]

    def _calculate_confidence(self, node: ASTNode, inferred_type: DataType) -> float:
        """计算类型推断置信度"""
        if inferred_type == DataType.UNKNOWN:
            return 0.0

        if node.node_type in [NodeType.DECLARATION, NodeType.TYPE_CAST]:
            return 1.0  # 显式类型声明，置信度最高

        if node.node_type == NodeType.LITERAL:
            return 0.9  # 字面量类型推断，置信度很高

        if node.node_type == NodeType.VARIABLE:
            return 0.8  # 变量类型查询，置信度较高

        return 0.6  # 其他情况，置信度中等

    def _extract_variable_name(self, declaration_or_assignment: str) -> Optional[str]:
        """从声明或赋值语句中提取变量名"""
        import re

        # 匹配声明: "type varname" 或 赋值: "varname ="
        decl_match = re.match(r'^(?:\w+\s+)?([a-zA-Z_][a-zA-Z0-9_]*)', declaration_or_assignment)
        if decl_match:
            return decl_match.group(1)

        return None

    def _is_mixed_precision_operation(self, type1: DataType, type2: DataType) -> bool:
        """检查是否是混合精度运算"""
        float_types = {DataType.FLOAT, DataType.FLOAT2, DataType.FLOAT3, DataType.FLOAT4}
        half_types = {DataType.HALF, DataType.HALF2, DataType.HALF3, DataType.HALF4}

        return (type1 in float_types and type2 in half_types) or \
               (type1 in half_types and type2 in float_types)

    def _get_node_inferred_type(self, node: ASTNode, type_table: Dict[str, DataType], result: LineAnalysisResult) -> DataType:
        """获取节点的推断类型"""
        # 在已分析的节点中查找
        for typed_node in result.typed_nodes:
            if typed_node.node == node:
                return typed_node.inferred_type

        # 如果没找到，进行简单推断
        return self._infer_node_type(node, [], type_table)


class SyntaxTreeAnalyzer:
    """语法树分析器 - 整合代码行分析和语法树构建"""

    def __init__(self):
        self.tree_builder = SyntaxTreeBuilder()

    def analyze_shader_with_syntax_trees(self, shader_content: str) -> Dict[str, Any]:
        """分析着色器代码并构建语法树"""
        # 首先使用代码行分析器获取有效代码行
        line_analyzer = CodeLineAnalyzer()
        code_lines = line_analyzer.analyze_shader_code(shader_content)

        # 对每行代码进行完整分析
        line_analyses = []
        global_type_table = {}

        for code_line in code_lines:
            # 分析单行代码，包括语法树构建、类型推断和模拟
            line_analysis = self.tree_builder.analyze_line_with_types(
                code_line.content,
                code_line.line_number,
                global_type_table.copy()
            )
            line_analyses.append(line_analysis)

            # 更新全局类型表
            global_type_table.update(line_analysis.variable_types)

        # 整合结果
        result = {
            'code_lines': code_lines,
            'line_analyses': line_analyses,
            'global_type_table': global_type_table,
            'tree_builder': self.tree_builder  # 保留构建器以便后续使用
        }

        # 格式化分析结果并保存到临时文件
        try:
            formatted_result = self.format_analysis_result(result)

            # 保存到临时文件
            import tempfile
            import os
            from datetime import datetime

            # 创建临时文件名，包含时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_filename = f"shader_analysis_{timestamp}.txt"
            temp_dir = tempfile.gettempdir()
            temp_filepath = os.path.join(temp_dir, temp_filename)

            with open(temp_filepath, 'w', encoding='utf-8') as f:
                f.write(formatted_result)

            print(f"📄 分析结果已保存到临时文件: {temp_filepath}")

            # 将临时文件路径添加到结果中
            result['temp_file_path'] = temp_filepath

        except Exception as e:
            print(f"⚠️ 保存分析结果到临时文件时出错: {str(e)}")
            result['temp_file_error'] = str(e)

        return result

    # 用于打印查看
    def format_analysis_result(self, analysis_result: Dict[str, Any]) -> str:
        """格式化分析结果"""
        result = []
        result.append("🌳 语法树分析结果")
        result.append("=" * 60)

        code_lines = analysis_result['code_lines']
        line_analyses = analysis_result['line_analyses']
        global_type_table = analysis_result['global_type_table']

        # 显示代码行和对应的分析结果
        result.append(f"识别到 {len(code_lines)} 行有效代码:")
        result.append("-" * 40)

        total_nodes = 0
        total_conversions = 0
        total_precision_issues = 0
        declared_variables = {}

        for code_line, line_analysis in zip(code_lines, line_analyses):
            result.append(f"\n第{line_analysis.line_number}行: {code_line.content}")

            if line_analysis.has_error:
                result.append(f"  ❌ 错误: {line_analysis.error_message}")
            else:
                result.append("语法树:")
                if line_analysis.syntax_tree:
                    result.append(self.tree_builder.print_tree(line_analysis.syntax_tree))

                # 显示模拟过程
                if line_analysis.simulation_data:
                    result.append("🔄 模拟过程:")
                    sim_data = line_analysis.simulation_data

                    if sim_data.get('has_simulation', False):
                        steps = sim_data.get('simulation_steps', [])
                        if steps:
                            for i, step in enumerate(steps, 1):
                                if isinstance(step, dict):
                                    # 字典格式的步骤
                                    target = step.get('target_var', 'unknown')
                                    operation = step.get('operation', 'unknown')
                                    operands = step.get('operands', [])
                                    expression = step.get('expression', '')

                                    result.append(f"  步骤{i}: {target} = {operation}({', '.join(map(str, operands))})")
                                    if expression:
                                        result.append(f"    表达式: {expression}")
                                else:
                                    # 对象格式的步骤
                                    result.append(f"  步骤{i}: {step.target_var} = {step.operation}({', '.join(map(str, step.operands))})")
                                    if hasattr(step, 'expression') and step.expression:
                                        result.append(f"    表达式: {step.expression}")

                        # 显示使用的临时变量
                        temp_vars = sim_data.get('temp_variables_used', [])
                        if temp_vars:
                            result.append(f"  临时变量: {', '.join(temp_vars)}")
                    else:
                        result.append("  无复杂运算需要模拟")

                    # 显示错误信息（如果有）
                    if 'error' in sim_data:
                        result.append(f"  ⚠️ 模拟错误: {sim_data['error']}")

                # 统计信息
                total_nodes += len(line_analysis.typed_nodes)
                total_conversions += len(line_analysis.type_conversions)
                total_precision_issues += len(line_analysis.precision_issues)
                declared_variables.update(line_analysis.variable_types)

        # 显示类型摘要
        result.append("📊 类型分析摘要:")
        result.append("-" * 40)
        result.append(f"总节点数: {total_nodes}")
        result.append(f"变量声明: {len(declared_variables)}")
        result.append(f"类型转换: {total_conversions}")
        result.append(f"精度问题: {total_precision_issues}")

        # 显示声明的变量
        if declared_variables:
            result.append(f"\n📝 声明的变量:")
            for var_name, var_type in declared_variables.items():
                result.append(f"  {var_name}: {var_type.value}")

        # 显示全局类型表
        if global_type_table:
            result.append(f"\n🌐 全局类型表:")
            for var_name, var_type in global_type_table.items():
                result.append(f"  {var_name}: {var_type.value}")

        return "\n".join(result)
